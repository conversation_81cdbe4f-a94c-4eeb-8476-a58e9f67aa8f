using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Requests.Application.Tickets.PatchTicket;

internal sealed class PatchTicketEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPatch("/api/v1/requests/tickets/{id}", async (
            Guid id,
            List<Operation<PatchTicketDto>> operations,
            //JsonPatchDocument<PatchTicketDto> patchDocument,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var contractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver();
            var command = new PatchTicketCommand
            {
                Id = id,
                PatchDocument = new JsonPatchDocument<PatchTicketDto>(operations, contractResolver)
            };

            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Requests.Tickets")
        .WithGroupName("apiv1")
        .Produces<Result>(StatusCodes.Status204NoContent)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .RequireAuthorization("Requests.Tickets");
    }
}
