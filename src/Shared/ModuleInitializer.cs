using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using Shared.Application.Behaviors;
using Shared.Application.EventBus;
using Shared.Domain;
using Shared.Infrastructure.Excel;
using Shared.Infrastructure.External.Email;
using Shared.Infrastructure.Localization;
using Shared.Utilities;
using static Shared.Application.Validation.RuleBuilderExtensions;

namespace Shared;

public static class ServiceRegistration
{
    public static IServiceCollection AddSharedModule(
        this IServiceCollection services,
        ConfigureHostBuilder builder,
        ConfigurationManager configuration)
    {
        builder.UseSerilog((context, loggerConfig) => loggerConfig.ReadFrom.Configuration(context.Configuration));
        services.AddSingleton(configuration.GetSection("AppSettings").Get<AppSettings>() ?? new AppSettings());
        services.Configure<AppSettings>(configuration.GetSection("AppSettings"));
        services.AddHybridCache(options =>
        {
            var cacheTime = configuration.GetValue<int?>("AppSettings:CacheTime") ?? 3600;
            options.DefaultEntryOptions = new HybridCacheEntryOptions
            {
                Expiration = TimeSpan.FromSeconds(cacheTime),
                LocalCacheExpiration = TimeSpan.FromSeconds(cacheTime)
            };
        });
        services.AddHostedService<IntegrationEventProcessorJob>();
        services.AddSingleton<InMemoryMessageQueue>();
        services.AddSingleton<IEventBus, InMemoryEventBus>();
        services.AddMediatR(config =>
        {
            config.RegisterServicesFromAssembly(typeof(ServiceRegistration).Assembly);
            config.AddOpenBehavior(typeof(RequestLoggingPipelineBehavior<,>));
            config.AddOpenBehavior(typeof(ValidationPipelineBehavior<,>));
        });
        services.AddValidatorsFromAssembly(typeof(ServiceRegistration).Assembly, includeInternalTypes: true);
        services.AddSignalR();
        services.AddScoped<IEmailManager, EmailManager>();
        services.AddSingleton<ILangService, LangService>();
        services.AddSingleton<ILocalizer, Localizer>();
        services.AddSingleton<ExcelHelper>();
        services.AddControllers(options =>
        {
            options.InputFormatters.Insert(0, CustomJPIF.GetJsonPatchInputFormatter());
        });
        var sp = services.BuildServiceProvider();
        var appSettings = sp.GetRequiredService<AppSettings>();
        PhoneNormalizer.Init(appSettings);
        return services;
    }
}
